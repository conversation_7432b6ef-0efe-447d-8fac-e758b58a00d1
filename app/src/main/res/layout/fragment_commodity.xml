<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    tools:context="com.ybmmarket20.fragments.CommodityFragment">

    <androidx.core.widget.NestedScrollView
        android:id="@+id/nsv_product_detail"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1">

        <LinearLayout
            android:id="@+id/container"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:baselineAligned="false"
            android:orientation="vertical">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:orientation="vertical">

                <androidx.constraintlayout.widget.ConstraintLayout
                    android:id="@+id/one"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical">

                    <!--产品图片-->
                    <FrameLayout
                        android:id="@+id/fl_detail_pic"
                        android:layout_width="match_parent"
                        android:layout_height="300dp"
                        android:background="@color/white"
                        app:layout_constraintLeft_toLeftOf="parent"
                        app:layout_constraintRight_toRightOf="parent"
                        app:layout_constraintTop_toTopOf="parent"
                        app:layout_scrollFlags="scroll|enterAlways">

                        <RelativeLayout
                            android:layout_width="match_parent"
                            android:layout_height="match_parent"
                            android:orientation="horizontal">

                            <com.ybmmarket20.view.CommodityBannerLayout
                                android:id="@+id/brand_iv"
                                android:layout_width="match_parent"
                                android:layout_height="match_parent" />

                            <TextView
                                android:id="@+id/tv_activity_price"
                                style="@style/activity_price"
                                android:layout_height="36dp"
                                android:layout_alignParentBottom="true"
                                android:layout_marginLeft="150dp"
                                android:text=""
                                android:textSize="20dp"
                                android:visibility="gone" />

                        </RelativeLayout>

                        <TextView
                            android:id="@+id/tv_sold_out"
                            android:layout_width="90dp"
                            android:layout_height="90dp"
                            android:layout_centerInParent="true"
                            android:layout_gravity="center"
                            android:background="@drawable/shop_limit01"
                            android:gravity="center"
                            android:text=""
                            android:textColor="#ffffff"
                            android:textSize="25sp"
                            android:visibility="gone" />

                        <ImageView
                            android:id="@+id/iv_video_paly"
                            android:layout_width="55dp"
                            android:layout_height="55dp"
                            android:layout_gravity="center"
                            android:src="@drawable/icon_video_play"
                            android:visibility="gone" />

                    </FrameLayout>

                    <include
                        layout="@layout/detail_time_promotion_layout"
                        android:layout_width="match_parent"
                        android:layout_height="65dp"
                        android:minHeight="60dp"
                        app:layout_constraintLeft_toLeftOf="parent"
                        app:layout_constraintRight_toRightOf="parent"
                        app:layout_constraintTop_toBottomOf="@id/fl_detail_pic" />

                    <include
                        layout="@layout/goods_details_spell_group_layout2"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:minHeight="60dp"
                        app:layout_constraintLeft_toLeftOf="parent"
                        app:layout_constraintRight_toRightOf="parent"
                        app:layout_constraintTop_toBottomOf="@id/rl_timing" />

                    <include
                        layout="@layout/goods_details_spell_group_limited_time_premium_layout"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:minHeight="60dp"
                        app:layout_constraintLeft_toLeftOf="parent"
                        app:layout_constraintRight_toRightOf="parent"
                        app:layout_constraintTop_toBottomOf="@id/ll_spell_group_root" />

                    <include
                        layout="@layout/goods_details_no_start_spell_group_layout2"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:minHeight="45dp"
                        app:layout_constraintLeft_toLeftOf="parent"
                        app:layout_constraintRight_toRightOf="parent"
                        app:layout_constraintTop_toBottomOf="@id/ll_spell_group_limited_time_premium"
                        tools:visibility="visible" />

                    <TextView
                        android:id="@+id/tv_price_prefix"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginLeft="@dimen/dimen_dp_10"
                        android:background="@drawable/bg_detail_price_prefix"
                        android:paddingLeft="@dimen/dimen_dp_5"
                        android:paddingTop="@dimen/dimen_dp_2"
                        android:paddingRight="@dimen/dimen_dp_5"
                        android:paddingBottom="@dimen/dimen_dp_2"
                        android:textColor="@color/white"
                        android:textSize="@dimen/dimen_dp_12"
                        android:visibility="gone"
                        app:layout_constraintBottom_toTopOf="@id/rl_timing"
                        app:layout_constraintLeft_toLeftOf="parent"
                        app:layout_constraintTop_toBottomOf="@id/fl_detail_pic"
                        tools:text="直降价"
                        tools:visibility="visible" />

                    <!--产品详情-->
                    <LinearLayout
                        android:id="@+id/ll_product_detail"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:background="@color/base_bg"
                        android:orientation="vertical"
                        app:layout_constraintLeft_toLeftOf="parent"
                        app:layout_constraintRight_toRightOf="parent"
                        app:layout_constraintTop_toBottomOf="@id/ll_no_start_spell_group_root"
                        app:layout_scrollFlags="scroll|enterAlways"
                        tools:visibility="visible">

                        <!--产品规格名称-->
                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:orientation="vertical">

                            <RelativeLayout
                                android:id="@+id/rl_price_layout"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:layout_marginTop="1dp"
                                android:background="@color/white"
                                android:orientation="horizontal"
                                android:paddingLeft="10dp"
                                android:paddingRight="10dp">

                                <LinearLayout
                                    android:layout_width="match_parent"
                                    android:layout_height="wrap_content"
                                    android:layout_centerVertical="true"
                                    android:gravity="center_vertical"
                                    android:orientation="vertical"
                                    android:paddingTop="2dp"
                                    android:paddingBottom="2dp">

                                    <RelativeLayout
                                        android:id="@+id/ll_normal_price_line"
                                        android:layout_width="wrap_content"
                                        android:layout_height="wrap_content"
                                        android:layout_gravity="center_vertical"
                                        android:layout_marginTop="2dp"
                                        android:gravity="center_vertical">

                                        <TextView
                                            android:id="@+id/tv_tax_amount"
                                            android:layout_width="wrap_content"
                                            android:layout_height="match_parent"
                                            android:layout_centerVertical="true"
                                            android:textColor="@color/tv_tax"
                                            android:textSize="16sp"
                                            android:textStyle="bold"
                                            tools:text="¥12.50" />

                                        <TextView
                                            android:id="@+id/unit"
                                            android:layout_width="wrap_content"
                                            android:layout_height="wrap_content"
                                            android:layout_alignBaseline="@id/tv_tax_amount"
                                            android:layout_marginLeft="3dp"
                                            android:layout_toRightOf="@id/tv_tax_amount"
                                            android:baselineAlignBottom="true"
                                            android:textColor="@color/color_9494A6"
                                            android:textSize="14sp"
                                            tools:text="元" />

                                        <ImageView
                                            android:id="@+id/ivZengPinTip"
                                            android:layout_width="@dimen/dimen_dp_12"
                                            android:layout_height="@dimen/dimen_dp_12"
                                            android:layout_alignTop="@+id/unit"
                                            android:layout_alignBottom="@+id/unit"
                                            android:layout_gravity="center"
                                            android:layout_marginStart="@dimen/dimen_dp_5"
                                            android:layout_toRightOf="@+id/unit"
                                            android:src="@drawable/icon_zengpin_tip"
                                            android:visibility="gone" />

                                        <com.ybmmarket20.common.widget.RoundTextView
                                            android:id="@+id/tv_original_price"
                                            style="@style/commodity_tv_style_02"
                                            android:layout_width="wrap_content"
                                            android:layout_height="@dimen/dimen_dp_20"
                                            android:layout_alignBaseline="@id/tv_tax_amount"
                                            android:layout_centerVertical="false"
                                            android:layout_toRightOf="@+id/unit"
                                            android:baselineAlignBottom="true"
                                            android:drawableRight="@drawable/orginal_pricemipmap"
                                            android:drawablePadding="5dp"
                                            android:gravity="center"
                                            android:paddingStart="@dimen/dimen_dp_5"
                                            android:paddingEnd="@dimen/dimen_dp_5"
                                            android:textColor="@color/gross_margin_color"
                                            android:textSize="12dp"
                                            android:visibility="gone"
                                            tools:text="折后价 ¥12.50"
                                            tools:visibility="visible" />

                                        <TextView
                                            android:id="@+id/tv_control"
                                            android:layout_width="wrap_content"
                                            android:layout_height="match_parent"
                                            android:layout_centerVertical="true"
                                            android:background="@color/white"
                                            android:text="暂无购买权限"
                                            android:textColor="@color/tv_control_new"
                                            android:textSize="18sp"
                                            android:visibility="gone" />

                                        <com.ybmmarket20.view.MyGridView
                                            android:id="@+id/tv_list"
                                            android:layout_width="wrap_content"
                                            android:layout_height="wrap_content"
                                            android:layout_marginRight="80dp"
                                            android:numColumns="3"
                                            android:visibility="gone"
                                            tools:visibility="visible" />

                                        <TextView
                                            android:id="@+id/tv_unit_price_detail"
                                            android:layout_width="wrap_content"
                                            android:layout_height="wrap_content"
                                            android:layout_alignBaseline="@id/tv_tax_amount"
                                            android:layout_toRightOf="@+id/tv_original_price"
                                            android:background="@color/colors_f5f5f5"
                                            android:paddingLeft="6dp"
                                            android:paddingTop="2dp"
                                            android:paddingRight="6dp"
                                            android:paddingBottom="2dp"
                                            android:textColor="@color/text_color_666666"
                                            android:textSize="11sp"
                                            android:visibility="gone"
                                            />
                                    </RelativeLayout>

                                    <LinearLayout
                                        android:layout_width="wrap_content"
                                        android:layout_height="wrap_content"
                                        android:orientation="horizontal">

                                        <!--建议零售价-->
                                        <RelativeLayout
                                            android:id="@+id/rl_suggested_retail_price_02"
                                            android:layout_width="wrap_content"
                                            android:layout_height="wrap_content"
                                            android:layout_marginTop="5dp"
                                            android:layout_marginRight="20dp">

                                            <TextView
                                                android:id="@+id/tv_layout_11"
                                                style="@style/commodity_tv_style_02"
                                                android:layout_width="wrap_content"
                                                android:textColor="@color/text_9494A6"
                                                android:textSize="12sp"
                                                tools:text="@string/detail_tv_suggested_retail_price" />

                                            <TextView
                                                android:id="@+id/tv_suggested_retail_price_02"
                                                style="@style/commodity_tv_style"
                                                android:layout_width="wrap_content"
                                                android:layout_toRightOf="@+id/tv_layout_11"
                                                android:textColor="@color/text_9494A6"
                                                android:textSize="12sp" />
                                        </RelativeLayout>

                                        <!--医保-->
                                        <RelativeLayout
                                            android:id="@+id/rl_health_insurance"
                                            android:layout_width="wrap_content"
                                            android:layout_height="wrap_content"
                                            android:layout_marginTop="5dp">

                                            <TextView
                                                android:id="@+id/tv_health_insurance_price"
                                                android:layout_width="wrap_content"
                                                android:layout_height="wrap_content"
                                                android:layout_centerVertical="true"
                                                android:singleLine="true"
                                                android:textColor="@color/text_9494A6"
                                                android:textSize="12sp"
                                                tools:text="医保支付价：¥100.00" />

                                        </RelativeLayout>

                                    </LinearLayout>

                                </LinearLayout>

                                <LinearLayout
                                    android:id="@+id/llFunction"
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:layout_alignParentEnd="true">

                                    <TextView
                                        android:id="@+id/tv_correction"
                                        android:layout_width="wrap_content"
                                        android:layout_height="match_parent"
                                        android:layout_marginTop="10dp"
                                        android:drawableTop="@drawable/icon_correction"
                                        android:drawablePadding="2dp"
                                        android:text="@string/correction"
                                        android:textColor="@color/color_292933"

                                        android:textSize="8sp" />

                                    <TextView
                                        android:id="@+id/tv_depreciate_inform"
                                        android:layout_width="wrap_content"
                                        android:layout_height="wrap_content"
                                        android:layout_marginStart="@dimen/dimen_dp_25"
                                        android:layout_marginTop="10dp"
                                        android:drawableTop="@drawable/icon_clock"
                                        android:drawablePadding="2dp"
                                        android:gravity="center"
                                        android:text="降价通知"
                                        android:textColor="@color/color_292933"
                                        android:textSize="8sp" />
                                </LinearLayout>

                            </RelativeLayout>

                            <TextView
                                android:id="@+id/tv_audit_passed_visible"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:background="@color/white"
                                android:paddingStart="@dimen/dimen_dp_10"
                                android:paddingTop="@dimen/dimen_dp_6"
                                android:paddingBottom="@dimen/dimen_dp_5"
                                android:text="价格认证资质可见"
                                android:textColor="#ff99664d"
                                android:textSize="18sp"
                                android:visibility="gone" />

                            <com.ybmmarket20.view.ProductDetailControlView
                                android:id="@+id/view_product_detail_control"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:visibility="gone" />

                            <!--温馨提示-->
                            <LinearLayout
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:background="@color/white"
                                android:orientation="vertical">

                                <TextView
                                    android:id="@+id/tv_layout_warm_prompt"
                                    android:layout_width="match_parent"
                                    android:layout_height="wrap_content"
                                    android:layout_gravity="center_vertical"
                                    android:layout_marginLeft="10dp"
                                    android:layout_marginTop="3dp"
                                    android:layout_marginRight="10dp"
                                    android:background="@color/detail_tv_FFF7EF"
                                    android:paddingTop="8dp"
                                    android:paddingBottom="8dp"
                                    android:text="@string/detail_tv_warm_prompt"
                                    android:textColor="@color/detail_tv_99664D"
                                    android:textSize="11sp"
                                    android:visibility="gone" />

                            </LinearLayout>

                            <RelativeLayout
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:background="@color/white"
                                android:orientation="vertical"
                                android:paddingLeft="10dp"
                                android:paddingTop="5dp"
                                android:paddingRight="10dp"
                                android:paddingBottom="8dp">

                                <LinearLayout
                                    android:id="@+id/lv_otc_name"
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:gravity="center_vertical"
                                    android:orientation="vertical">

                                    <TextView
                                        android:id="@+id/tv_limited"
                                        android:layout_width="match_parent"
                                        android:layout_height="wrap_content"
                                        android:layout_marginTop="8dp"
                                        android:layout_marginRight="8dp"
                                        android:layout_marginBottom="@dimen/dimen_dp_8"
                                        android:background="@drawable/shape_2corner_fff7e9"
                                        android:ellipsize="end"
                                        android:lineSpacingExtra="@dimen/dimen_dp_2"
                                        android:maxLines="2"
                                        android:padding="@dimen/dimen_dp_5"
                                        android:textColor="@color/text_color_333333"
                                        android:textSize="@dimen/text_12"
                                        android:visibility="gone"
                                        app:layout_constraintLeft_toLeftOf="@id/iv_good"
                                        app:layout_constraintRight_toRightOf="parent"
                                        app:layout_constraintTop_toBottomOf="@id/layout_subtotal_and_edit"
                                        tools:text="购买79盒及以下时可享受11.85元/盒，超过79盒的部分恢复12.25元/盒"
                                        tools:visibility="visible" />

                                    <TextView
                                        android:id="@+id/tv_name"
                                        android:layout_width="match_parent"
                                        android:layout_height="wrap_content"
                                        android:ellipsize="end"
                                        android:gravity="center_vertical"
                                        android:maxLines="2"
                                        android:text=""
                                        android:textColor="@color/color_292933"
                                        android:textSize="16sp"
                                        android:textStyle="bold"
                                        tools:text="江中牌健胃消食片  " />

                                </LinearLayout>

                                <TextView
                                    android:id="@+id/tv_subtitle"
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:layout_below="@+id/lv_otc_name"
                                    android:ellipsize="end"
                                    android:gravity="center_vertical"
                                    android:text=""
                                    android:textColor="@color/colors_99664D"
                                    android:textSize="12sp"
                                    tools:text="副标题" />

                                <com.google.android.flexbox.FlexboxLayout
                                    android:id="@+id/flexContent"
                                    android:layout_width="match_parent"
                                    android:layout_height="wrap_content"
                                    android:layout_below="@+id/tv_subtitle"
                                    android:layout_marginTop="5dp"
                                    android:orientation="horizontal"
                                    app:flexDirection="row"
                                    app:flexWrap="wrap"
                                    app:justifyContent="flex_start">

                                    <!--规格-->
                                    <RelativeLayout
                                        android:id="@+id/rl_spec"
                                        android:layout_width="wrap_content"
                                        android:layout_height="wrap_content"
                                        android:layout_marginTop="5dp"
                                        android:layout_marginEnd="20dp">

                                        <TextView
                                            android:id="@+id/tv_layout_01"
                                            style="@style/commodity_tv_style_02"
                                            android:layout_width="wrap_content"
                                            android:text="@string/detail_tv_spec"
                                            android:textColor="@color/detail_tv_575757"
                                            android:textSize="13sp" />

                                        <TextView
                                            android:id="@+id/tv_spec"
                                            style="@style/commodity_tv_style"
                                            android:layout_width="wrap_content"
                                            android:layout_height="wrap_content"
                                            android:layout_centerVertical="true"
                                            android:layout_toEndOf="@+id/tv_layout_01"
                                            android:singleLine="true"
                                            android:textColor="@color/detail_tv_575757"
                                            android:textSize="13sp"
                                            tools:text="150mg*12粒/板*1板" />
                                    </RelativeLayout>

                                    <!--库存-->
                                    <RelativeLayout
                                        android:id="@+id/rl_limit_price"
                                        android:layout_width="wrap_content"
                                        android:layout_height="wrap_content"
                                        android:layout_marginTop="5dp"
                                        android:background="@color/white">

                                        <TextView
                                            android:id="@+id/tv_repertory"
                                            style="@style/commodity_tv_style"
                                            android:layout_width="wrap_content"
                                            android:layout_height="wrap_content"
                                            android:layout_centerVertical="true"
                                            android:gravity="center"
                                            android:singleLine="true"
                                            android:textColor="@color/detail_tv_575757"
                                            android:textSize="13sp"
                                            tools:text="库存:10000件" />

                                        <com.ybmmarket20.common.widget.RoundTextView
                                            android:id="@+id/tv_limit2"
                                            android:layout_width="wrap_content"
                                            android:layout_height="wrap_content"
                                            android:layout_centerVertical="true"
                                            android:layout_marginStart="9dp"
                                            android:layout_toEndOf="@+id/tv_repertory"
                                            android:background="@drawable/forget_red_bg"
                                            android:paddingLeft="4dp"
                                            android:paddingTop="2dp"
                                            android:paddingRight="4dp"
                                            android:paddingBottom="2dp"
                                            android:text="20件起购"
                                            android:textColor="@color/detail_tv_F96A25"
                                            android:textSize="9dp"
                                            android:visibility="gone"
                                            app:rv_backgroundColor="@color/detail_tv_0DF96A25"
                                            app:rv_cornerRadius="1dp"
                                            app:rv_strokeColor="@color/detail_tv_80f96a25"
                                            app:rv_strokeWidth="0.5dp"
                                            tools:visibility="visible" />

                                        <com.ybmmarket20.common.widget.RoundTextView
                                            android:id="@+id/tv_limit"
                                            android:layout_width="wrap_content"
                                            android:layout_height="wrap_content"
                                            android:layout_centerVertical="true"
                                            android:layout_marginStart="9dp"
                                            android:layout_toEndOf="@+id/tv_limit2"
                                            android:background="@drawable/forget_red_bg"
                                            android:paddingLeft="4dp"
                                            android:paddingTop="2dp"
                                            android:paddingRight="4dp"
                                            android:paddingBottom="2dp"
                                            android:textColor="@color/detail_tv_F96A25"
                                            android:textSize="9dp"
                                            android:visibility="gone"
                                            app:rv_backgroundColor="@color/detail_tv_0DF96A25"
                                            app:rv_cornerRadius="1dp"
                                            app:rv_strokeColor="@color/detail_tv_80f96a25"
                                            app:rv_strokeWidth="0.5dp"
                                            tools:text="限购20件"
                                            tools:visibility="visible" />

                                    </RelativeLayout>
                                    <!--麻黄碱-->
                                    <com.ybmmarket20.common.widget.RoundTextView
                                        android:id="@+id/tv_ephedrine"
                                        android:layout_width="wrap_content"
                                        android:layout_height="wrap_content"
                                        android:layout_marginTop="5dp"
                                        android:background="@drawable/forget_red_bg"
                                        android:paddingLeft="4dp"
                                        android:paddingTop="2dp"
                                        android:paddingRight="4dp"
                                        android:paddingBottom="2dp"
                                        android:text="@string/str_comodity_tag_ephedrine"
                                        android:textColor="@color/detail_tv_F96A25"
                                        android:textSize="9dp"
                                        android:visibility="gone"
                                        app:rv_backgroundColor="@color/detail_tv_0DF96A25"
                                        app:rv_cornerRadius="1dp"
                                        app:rv_strokeColor="@color/detail_tv_80f96a25"
                                        app:rv_strokeWidth="0.5dp"
                                        tools:visibility="visible" />
                                </com.google.android.flexbox.FlexboxLayout>

                                <ScrollView
                                    android:id="@+id/tv_subtags"
                                    android:layout_width="match_parent"
                                    android:layout_height="wrap_content"
                                    android:layout_below="@id/flexContent"
                                    android:visibility="visible"
                                    android:layout_marginTop="6dp"
                                    >
                                </ScrollView>

                                <com.ybmmarket20.common.widget.RoundFrameLayout
                                    android:id="@+id/fyCnAttr"
                                    android:layout_width="match_parent"
                                    android:layout_height="wrap_content"
                                    android:layout_below="@id/flexContent"
                                    android:layout_marginTop="6dp"
                                    android:visibility="gone"
                                    app:rv_backgroundColor="@color/color_f8f8f8"
                                    app:rv_cornerRadius="7dp">

                                    <androidx.recyclerview.widget.RecyclerView
                                        android:id="@+id/rlvCnAttr"
                                        android:layout_width="wrap_content"
                                        android:layout_height="wrap_content"
                                        android:orientation="horizontal"
                                        app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager" />

                                    <ImageButton
                                        android:id="@+id/ivCnAttrArrow"
                                        android:layout_width="50dp"
                                        android:layout_height="58dp"
                                        android:layout_gravity="end|center_vertical"
                                        android:background="@drawable/gradient_trans_f8f8f8"
                                        android:paddingStart="22dp"
                                        android:src="@drawable/icon_cart_shop_arrow" />
                                </com.ybmmarket20.common.widget.RoundFrameLayout>
                            </RelativeLayout>

                            <!--横排显示标签-->
                            <LinearLayout
                                android:id="@+id/ll_label"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:background="@color/white"
                                android:minHeight="37dp"
                                android:orientation="horizontal"
                                android:paddingLeft="10dp"
                                android:paddingTop="7dp"
                                android:paddingRight="10dp"
                                android:visibility="gone">

                                <com.ybmmarket20.common.widget.RoundTextView
                                    android:id="@+id/tv_health_insurance"
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:layout_marginRight="5dp"
                                    android:gravity="center_vertical"
                                    android:paddingLeft="4dp"
                                    android:paddingTop="1dp"
                                    android:paddingRight="4dp"
                                    android:paddingBottom="1dp"
                                    android:text="国家医保"
                                    android:textColor="@color/colors_00b3b3"
                                    android:textSize="12sp"
                                    android:visibility="gone"
                                    app:rv_backgroundColor="@color/colors_0d00b3b3"
                                    app:rv_cornerRadius="1dp"
                                    app:rv_strokeColor="@color/colors_8000b3b3"
                                    app:rv_strokeWidth="0.5dp" />

                                <com.ybmmarket20.common.widget.RoundTextView
                                    android:id="@+id/iv_exclusive"
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:gravity="center_vertical"
                                    android:paddingLeft="4dp"
                                    android:paddingTop="1dp"
                                    android:paddingRight="4dp"
                                    android:paddingBottom="1dp"
                                    android:text="独家"
                                    android:textColor="@color/colors_8F7031"
                                    android:textSize="12sp"
                                    android:visibility="gone"
                                    app:rv_backgroundColor="@color/colors_0D8F7031"
                                    app:rv_cornerRadius="1dp"
                                    app:rv_strokeColor="@color/colors_808F7031"
                                    app:rv_strokeWidth="0.5dp" />

                            </LinearLayout>
                            <FrameLayout
                                android:id="@+id/layoutCombined"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:orientation="vertical"
                                android:visibility="gone"
                                android:layout_marginTop="@dimen/dimen_dp_10"
                                android:background="@color/white"
                                android:padding="@dimen/dimen_dp_8">
                            </FrameLayout>

                            <!-- 次日达 -->
                            <androidx.constraintlayout.widget.ConstraintLayout
                                android:id="@+id/rl_next_day"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:layout_marginTop="10dp"
                                android:background="@color/white"
                                android:paddingLeft="@dimen/dimen_dp_10"
                                android:paddingTop="@dimen/dimen_dp_12"
                                android:paddingBottom="@dimen/dimen_dp_10"
                                android:visibility="gone"
                                tools:visibility="visible">

                                <TextView
                                    android:id="@+id/tv_next_day_label"
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:paddingStart="3dp"
                                    android:paddingTop="2dp"
                                    android:paddingEnd="3dp"
                                    android:paddingBottom="2dp"
                                    android:text="次日达"
                                    android:textSize="10sp"
                                    app:layout_constraintBottom_toBottomOf="parent"
                                    app:layout_constraintStart_toStartOf="parent"
                                    app:layout_constraintTop_toTopOf="parent" />

                                <TextView
                                    android:id="@+id/tv_next_day"
                                    android:layout_width="0dp"
                                    android:layout_height="wrap_content"
                                    android:layout_marginStart="5dp"
                                    android:layout_marginEnd="8dp"
                                    android:textColor="@color/color_292933"
                                    android:textSize="12sp"
                                    app:layout_constraintBottom_toBottomOf="parent"
                                    app:layout_constraintEnd_toEndOf="parent"
                                    app:layout_constraintStart_toEndOf="@id/tv_next_day_label"
                                    app:layout_constraintTop_toTopOf="parent"
                                    tools:text="17:00前付款，预计明天(3月18日)23:59前送达" />
                            </androidx.constraintlayout.widget.ConstraintLayout>

                            <com.ybmmarket20.view.sameSpecifications.SameSpecificationsListView
                                android:id="@+id/same_specification_list_view"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:layout_marginTop="15dp"
                                android:visibility="gone"
                                tools:visibility="visible" />

                            <!-- 优惠 -->
                            <androidx.constraintlayout.widget.ConstraintLayout
                                android:id="@+id/rl_coupon_or_promotion"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:layout_marginTop="10dp"
                                android:background="@color/white"
                                android:paddingLeft="@dimen/dimen_dp_10"
                                android:paddingTop="@dimen/dimen_dp_15"
                                android:paddingRight="@dimen/dimen_dp_10"
                                android:paddingBottom="@dimen/dimen_dp_15"
                                android:visibility="gone"
                                tools:visibility="visible">


                                <LinearLayout
                                    android:layout_width="0dp"
                                    android:layout_height="wrap_content"
                                    android:orientation="vertical"
                                    app:layout_constraintLeft_toLeftOf="parent"
                                    app:layout_constraintRight_toRightOf="parent"
                                    app:layout_constraintTop_toTopOf="parent">

                                    <TextView
                                        android:layout_width="wrap_content"
                                        android:layout_height="wrap_content"
                                        android:layout_marginRight="@dimen/dimen_dp_6"
                                        android:text="优惠:"
                                        android:textColor="@color/text_292933"
                                        android:textSize="@dimen/dimen_dp_12" />

                                    <LinearLayout
                                        android:id="@+id/ll_new_tag"
                                        android:layout_width="match_parent"
                                        android:layout_height="wrap_content"
                                        android:layout_marginTop="@dimen/dimen_dp_10"
                                        android:orientation="horizontal"
                                        android:visibility="gone">

                                        <androidx.constraintlayout.widget.ConstraintLayout
                                            android:id="@+id/cl_new_tag"
                                            android:layout_width="wrap_content"
                                            android:layout_height="wrap_content"
                                            android:layout_marginRight="@dimen/dimen_dp_5"
                                            android:visibility="gone">

                                            <TextView
                                                android:id="@+id/tv_new_tag_1"
                                                style="@style/detail_promotion_tv"
                                                android:paddingStart="@dimen/dimen_dp_5"
                                                android:paddingTop="@dimen/dimen_dp_2"
                                                android:paddingEnd="@dimen/dimen_dp_5"
                                                android:paddingBottom="@dimen/dimen_dp_2"
                                                android:text="整单包邮"
                                                app:layout_constraintLeft_toLeftOf="parent"
                                                app:layout_constraintTop_toTopOf="parent" />

                                            <TextView
                                                android:id="@+id/tv_new_tag_2"
                                                style="@style/detail_promotion_tv2"
                                                android:paddingStart="@dimen/dimen_dp_5"
                                                android:paddingTop="@dimen/dimen_dp_2"
                                                android:paddingEnd="@dimen/dimen_dp_5"
                                                android:paddingBottom="@dimen/dimen_dp_2"
                                                android:text="同笔订单商品均享受包邮"
                                                android:translationX="-1.5dp"
                                                app:layout_constraintStart_toEndOf="@id/tv_new_tag_1"
                                                app:layout_constraintTop_toTopOf="parent" />

                                        </androidx.constraintlayout.widget.ConstraintLayout>

                                        <TextView
                                            android:id="@+id/tv_new_tag_3"
                                            style="@style/detail_promotion_tv2"
                                            android:paddingStart="@dimen/dimen_dp_5"
                                            android:paddingTop="@dimen/dimen_dp_2"
                                            android:paddingEnd="@dimen/dimen_dp_5"
                                            android:paddingBottom="@dimen/dimen_dp_2"
                                            android:text="不可用券"
                                            android:visibility="gone"
                                            app:layout_constraintStart_toEndOf="@id/cl_new_tag"
                                            app:layout_constraintTop_toTopOf="parent" />

                                    </LinearLayout>

                                    <!--优惠券-->
                                    <RelativeLayout
                                        android:id="@+id/rl_coupon"
                                        android:layout_width="match_parent"
                                        android:layout_height="wrap_content"
                                        android:layout_marginTop="@dimen/dimen_dp_10"
                                        android:gravity="center_vertical"
                                        android:visibility="gone"
                                        tools:visibility="visible">

                                        <TextView
                                            android:id="@+id/tv_coupon_title_01"
                                            android:layout_width="wrap_content"
                                            android:layout_height="wrap_content"
                                            android:layout_marginRight="@dimen/dimen_dp_6"
                                            android:text="优惠:"
                                            android:textColor="@color/text_292933"
                                            android:textSize="@dimen/dimen_dp_12"
                                            android:visibility="gone" />

                                        <TextView
                                            android:id="@+id/tv_coupon_title"
                                            android:layout_width="wrap_content"
                                            android:layout_height="@dimen/dimen_dp_15"
                                            android:layout_alignBaseline="@id/tv_coupon_title_01"
                                            android:layout_gravity="center_vertical"
                                            android:layout_marginRight="@dimen/dimen_dp_10"
                                            android:layout_toRightOf="@id/tv_coupon_title_01"
                                            android:paddingLeft="4dp"
                                            android:paddingRight="4dp"
                                            android:text=""
                                            android:textSize="@dimen/dimen_dp_10"
                                            tools:text="领券" />

                                        <TextView
                                            android:id="@+id/tv_coupon_one"
                                            style="@style/detail_coupon_base"
                                            android:layout_alignBaseline="@id/tv_coupon_title"
                                            android:layout_marginRight="8dp"
                                            android:layout_toRightOf="@id/tv_coupon_title"
                                            android:text=""
                                            tools:visibility="visible" />

                                        <TextView
                                            android:id="@+id/tv_coupon_two"
                                            style="@style/detail_coupon_base"
                                            android:layout_alignBaseline="@id/tv_coupon_title"
                                            android:layout_toRightOf="@+id/tv_coupon_one"
                                            android:text=""
                                            tools:visibility="visible" />

                                    </RelativeLayout>

                                    <!--促销标签-->
                                    <LinearLayout
                                        android:layout_width="match_parent"
                                        android:layout_height="wrap_content"
                                        android:layout_marginTop="@dimen/dimen_dp_10"
                                        android:orientation="horizontal">

                                        <TextView
                                            android:id="@+id/tv_coupon_title_02"
                                            android:layout_width="wrap_content"
                                            android:layout_height="wrap_content"
                                            android:layout_marginRight="@dimen/dimen_dp_6"
                                            android:text="优惠:"
                                            android:textColor="@color/text_292933"
                                            android:textSize="@dimen/dimen_dp_12"
                                            android:visibility="invisible" />

                                        <LinearLayout
                                            android:id="@+id/ll_show_promotion"
                                            android:layout_width="match_parent"
                                            android:layout_height="wrap_content"
                                            android:orientation="vertical"
                                            android:visibility="gone"
                                            tools:visibility="visible">

                                            <LinearLayout
                                                android:id="@+id/ll_01"
                                                style="@style/detail_promotion_base"
                                                android:layout_height="wrap_content"
                                                android:gravity="center_vertical"
                                                tools:visibility="visible">

                                                <TextView
                                                    android:id="@+id/tv_icon_type_01"
                                                    style="@style/detail_promotion_tv"
                                                    android:text=""
                                                    tools:text="促销标签" />

                                                <ImageView
                                                    android:id="@+id/iv_icon_type_01"
                                                    style="@style/detail_promotion_iv"
                                                    android:visibility="gone" />

                                                <TextView
                                                    android:id="@+id/tv_content_type_01"
                                                    style="@style/detail_promotion_tv02"
                                                    android:layout_marginStart="5dp"
                                                    android:layout_marginTop="@dimen/dimen_dp_1"
                                                    android:layout_marginEnd="@dimen/dimen_dp_30"
                                                    android:maxLines="10"
                                                    android:singleLine="false"
                                                    android:text=""
                                                    android:textColor="@color/color_292933"
                                                    android:textSize="13sp"
                                                    tools:text="sdfadfasdfadfasdf" />

                                            </LinearLayout>

                                            <com.ybmmarket20.view.CSUListView
                                                android:id="@+id/csu_01"
                                                android:layout_width="match_parent"
                                                android:layout_height="90dp"
                                                android:layout_marginTop="@dimen/dimen_dp_10" />

                                            <LinearLayout
                                                android:id="@+id/ll_02"
                                                style="@style/detail_promotion_base"
                                                android:layout_height="wrap_content"
                                                android:layout_marginTop="@dimen/dimen_dp_10"
                                                android:gravity="top">

                                                <TextView
                                                    android:id="@+id/tv_icon_type_02"
                                                    style="@style/detail_promotion_tv"
                                                    android:text="" />

                                                <ImageView
                                                    android:id="@+id/iv_icon_type_02"
                                                    style="@style/detail_promotion_iv"
                                                    android:visibility="gone" />

                                                <TextView
                                                    android:id="@+id/tv_content_type_02"
                                                    style="@style/detail_promotion_tv02"
                                                    android:maxLines="10"
                                                    android:singleLine="false"
                                                    android:text=""
                                                    android:textColor="@color/color_292933"
                                                    android:textSize="13sp" />

                                            </LinearLayout>

                                            <com.ybmmarket20.view.CSUListView
                                                android:id="@+id/csu_02"
                                                android:layout_width="match_parent"
                                                android:layout_height="90dp"
                                                android:layout_marginTop="@dimen/dimen_dp_10" />

                                            <LinearLayout
                                                android:id="@+id/ll_03"
                                                style="@style/detail_promotion_base"
                                                android:layout_height="wrap_content"
                                                android:layout_marginTop="@dimen/dimen_dp_10"
                                                android:gravity="top">

                                                <TextView
                                                    android:id="@+id/tv_icon_type_03"
                                                    style="@style/detail_promotion_tv"
                                                    android:text="" />

                                                <ImageView
                                                    android:id="@+id/iv_icon_type_03"
                                                    style="@style/detail_promotion_iv"
                                                    android:visibility="gone" />

                                                <TextView
                                                    android:id="@+id/tv_content_type_03"
                                                    style="@style/detail_promotion_tv02"
                                                    android:maxLines="10"
                                                    android:singleLine="false"
                                                    android:text=""
                                                    android:textColor="@color/color_292933"
                                                    android:textSize="13sp" />

                                            </LinearLayout>

                                            <com.ybmmarket20.view.CSUListView
                                                android:id="@+id/csu_03"
                                                android:layout_width="match_parent"
                                                android:layout_height="90dp"
                                                android:layout_marginTop="@dimen/dimen_dp_10" />

                                        </LinearLayout>
                                    </LinearLayout>
                                </LinearLayout>

                                <com.ybmmarket20.common.widget.RoundTextView
                                    android:id="@+id/iv_promotion_more"
                                    style="@style/commodity_tv_style_02"
                                    android:layout_width="wrap_content"
                                    android:layout_height="@dimen/dimen_dp_20"
                                    android:layout_centerVertical="false"
                                    android:layout_marginStart="10dp"
                                    android:layout_marginLeft="10dp"
                                    android:baselineAlignBottom="true"
                                    android:drawablePadding="5dp"
                                    android:gravity="center"
                                    android:paddingStart="@dimen/dimen_dp_10"
                                    android:paddingEnd="@dimen/dimen_dp_10"
                                    android:text="更多优惠"
                                    android:textColor="@color/color_ff2121"
                                    android:textSize="12dp"
                                    android:visibility="visible"
                                    app:layout_constraintRight_toRightOf="parent"
                                    app:layout_constraintTop_toTopOf="parent"
                                    app:rv_cornerRadius="@dimen/dimen_dp_10"
                                    app:rv_strokeColor="@color/color_ff2121"
                                    app:rv_strokeWidth="1dp" />

                                <!-- 为了覆盖更多优惠那块视图的点击逻辑 -->
                                <View
                                    android:id="@+id/view_promotion_more"
                                    android:layout_width="match_parent"
                                    android:layout_height="@dimen/dimen_dp_20"
                                    app:layout_constraintLeft_toLeftOf="parent"
                                    app:layout_constraintTop_toTopOf="parent"
                                    tools:ignore="MissingConstraints" />

                                <!--                                <ImageView-->
                                <!--                                    android:id="@+id/iv_promotion_more"-->
                                <!--                                    android:layout_width="27dp"-->
                                <!--                                    android:layout_height="15dp"-->
                                <!--                                    android:layout_marginRight="@dimen/dimen_dp_10"-->
                                <!--                                    android:src="@drawable/icon_more_dots"-->
                                <!--                                    app:layout_constraintRight_toRightOf="parent"-->
                                <!--                                    app:layout_constraintTop_toTopOf="parent" />-->
                            </androidx.constraintlayout.widget.ConstraintLayout>

                            <!-- 推荐理由-->
                            <LinearLayout
                                android:id="@+id/ll_recommend_word"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:background="@color/color_0dff2121"
                                android:orientation="vertical"
                                android:paddingTop="10dp"
                                android:paddingBottom="10dp"
                                android:visibility="gone"
                                tools:visibility="visible">

                                <TextView
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:layout_marginStart="10dp"
                                    android:text="@string/str_fragment_commodity_recommend_word_tips"
                                    android:textColor="@color/text_292933"
                                    android:textSize="12dp" />

                                <TextView
                                    android:id="@+id/tv_recommend_word_zan"
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:layout_marginStart="24dp"
                                    android:layout_marginTop="11dp"
                                    android:drawableStart="@drawable/icon_recommend_word_zan"
                                    android:drawablePadding="14dp"
                                    android:ellipsize="end"
                                    android:maxLines="1"
                                    android:textColor="@color/text_292933"
                                    android:textSize="12dp"
                                    tools:text="缓解感冒症状，不含有伪麻黄碱不含有伪麻黄碱" />

                                <TextView
                                    android:id="@+id/tv_recommend_word_star"
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:layout_marginStart="24dp"
                                    android:layout_marginTop="11dp"
                                    android:drawableStart="@drawable/icon_recommend_word_star"
                                    android:drawablePadding="14dp"
                                    android:ellipsize="end"
                                    android:maxLines="1"
                                    android:textColor="@color/text_292933"
                                    android:textSize="12dp"
                                    tools:text="优质自产胶囊剂，杂质含量低" />

                                <TextView
                                    android:id="@+id/tv_recommend_word_check"
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:layout_marginStart="24dp"
                                    android:layout_marginTop="11dp"
                                    android:drawableStart="@drawable/icon_recommend_word_check"
                                    android:drawablePadding="14dp"
                                    android:ellipsize="end"
                                    android:maxLines="1"
                                    android:textColor="@color/text_292933"
                                    android:textSize="12dp"
                                    tools:text="欧盟标准生产，全程品质监控，质量更稳定" />
                            </LinearLayout>

                            <!--拼团中-->
                            <LinearLayout
                                android:id="@+id/ll_spell_group_sub_title"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:layout_marginTop="@dimen/dimen_dp_10"
                                android:background="@color/white"
                                android:orientation="vertical"
                                android:visibility="gone"
                                tools:visibility="visible">

                                <LinearLayout
                                    android:layout_width="match_parent"
                                    android:layout_height="wrap_content"
                                    android:gravity="center_vertical"
                                    android:minHeight="40dp"
                                    android:orientation="horizontal"
                                    android:paddingStart="10dp"
                                    android:paddingEnd="10dp">

                                    <TextView
                                        android:layout_width="wrap_content"
                                        android:layout_height="wrap_content"
                                        android:background="@drawable/shape_spell_group_tag"
                                        android:paddingLeft="3.5dp"
                                        android:paddingRight="3.5dp"
                                        android:text="拼团中"
                                        android:textColor="@color/white"
                                        android:textSize="@dimen/dimen_dp_10" />

                                    <TextView
                                        android:id="@+id/tv_spell_group_sub_title"
                                        android:layout_width="wrap_content"
                                        android:layout_height="wrap_content"
                                        android:layout_marginStart="7dp"
                                        android:textColor="@color/color_292933"
                                        android:textSize="13sp"
                                        tools:text="500个客户已参与拼团" />

                                    <TextView
                                        android:id="@+id/tv_spell_group_already"
                                        android:layout_width="wrap_content"
                                        android:layout_height="wrap_content"
                                        android:textColor="@color/color_292933"
                                        android:textSize="13sp"
                                        tools:text="/已拼400盒" />
                                </LinearLayout>

                                <!--轮播-->
                                <com.ybmmarket20.view.MarqueeViewSpellGroup
                                    android:id="@+id/marquee_view"
                                    android:layout_width="match_parent"
                                    android:layout_height="wrap_content"
                                    android:background="@color/white"
                                    android:paddingBottom="10dp"
                                    app:isSetAnimDuration="true" />

                            </LinearLayout>

                            <!-- 拼团推荐 -->
                            <com.ybmmarket20.view.homesteady.HomeSteadySpellGroupView
                                android:id="@+id/spell_group_view"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:layout_marginTop="@dimen/dimen_dp_10"
                                android:visibility="gone"
                                tools:visibility="visible" />

                            <!--暂时没用-->
                            <LinearLayout
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:orientation="vertical">

                                <!--控销价-->
                                <RelativeLayout
                                    android:id="@+id/ly_product_price_kxj_ml"
                                    android:layout_width="match_parent"
                                    android:layout_height="44dp"
                                    android:background="@color/white"
                                    android:paddingLeft="10dp"
                                    android:paddingRight="10dp"
                                    android:visibility="gone">

                                    <TextView
                                        android:id="@+id/tv_ontrol_market"
                                        style="@style/commodity_tv_style_02"
                                        tools:text="控销价" />

                                    <RelativeLayout
                                        android:id="@+id/shop_price_layout"
                                        android:layout_width="match_parent"
                                        android:layout_height="match_parent"
                                        android:layout_toRightOf="@+id/tv_ontrol_market">

                                        <TextView
                                            android:id="@+id/tv_product_price_kxj"
                                            style="@style/brand_item_kxj_lsj_base"
                                            android:layout_width="wrap_content"
                                            android:layout_height="wrap_content"
                                            android:text="¥20.05"
                                            android:textColor="@color/text_9494A6"
                                            android:textSize="15sp"
                                            android:visibility="visible" />

                                        <TextView
                                            android:id="@+id/tv_product_price_ml"
                                            style="@style/brand_item_kxj_lsj_base"
                                            android:layout_width="wrap_content"
                                            android:layout_height="wrap_content"
                                            android:layout_toRightOf="@+id/tv_product_price_kxj"
                                            android:maxLines="1"
                                            android:singleLine="true"
                                            android:text="（毛利率 40%）"
                                            android:textColor="@color/text_9494A6"
                                            android:textSize="15sp"
                                            android:visibility="visible" />
                                    </RelativeLayout>
                                </RelativeLayout>
                            </LinearLayout>

                            <!--暂时没用-->
                            <RelativeLayout
                                android:id="@+id/rl_third_party"
                                android:layout_width="match_parent"
                                android:layout_height="44dp"
                                android:background="@color/white"
                                android:gravity="center_vertical"
                                android:paddingLeft="10dp"
                                android:visibility="gone">

                                <RelativeLayout
                                    android:id="@+id/rl2_third_party"
                                    android:layout_width="@dimen/detail_tv_wrap_91dp"
                                    android:layout_height="match_parent">

                                    <com.ybmmarket20.common.widget.RoundTextView
                                        android:id="@+id/tv_third_party"
                                        android:layout_width="wrap_content"
                                        android:layout_height="wrap_content"
                                        android:layout_centerVertical="true"
                                        android:paddingLeft="5dp"
                                        android:paddingTop="3dp"
                                        android:paddingRight="5dp"
                                        android:paddingBottom="3dp"
                                        android:text="第三方发货"
                                        android:textColor="@color/color_9494A6"
                                        android:textSize="@dimen/detail_tv_dimen_14sp"
                                        app:rv_cornerRadius="2dp"
                                        app:rv_strokeColor="@color/color_9494A6"
                                        app:rv_strokeWidth="1dp" />

                                </RelativeLayout>

                                <TextView
                                    android:id="@+id/tv_manufacturers"
                                    android:layout_width="match_parent"
                                    android:layout_height="match_parent"
                                    android:layout_centerVertical="true"
                                    android:layout_toRightOf="@+id/rl2_third_party"
                                    android:drawableRight="@drawable/icon_show_promition_action"
                                    android:gravity="center_vertical"
                                    android:maxLines="1"
                                    android:paddingRight="10dp"
                                    android:singleLine="true"
                                    android:text=""
                                    android:textColor="#292933"
                                    android:textSize="14sp" />
                            </RelativeLayout>
                            <!-- 套餐-->
                            <LinearLayout
                                android:id="@+id/ll_combo"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:layout_marginTop="10dp"
                                android:background="@color/white"
                                android:orientation="vertical"
                                android:visibility="gone"
                                tools:visibility="gone">

                                <LinearLayout
                                    android:layout_width="match_parent"
                                    android:layout_height="49dp"
                                    android:gravity="center_vertical">

                                    <com.flyco.tablayout.SlidingTabLayout
                                        android:id="@+id/stl_combo"
                                        android:layout_width="0dp"
                                        android:layout_height="match_parent"
                                        android:layout_weight="1"
                                        android:background="#00FFFFFF"
                                        android:orientation="horizontal"
                                        app:tl_indicator_color="@color/base_colors_new"
                                        app:tl_indicator_corner_radius="2dp"
                                        app:tl_indicator_height="4dp"
                                        app:tl_indicator_margin_bottom="6dp"
                                        app:tl_indicator_width_equal_title="true"
                                        app:tl_tab_space_equal="false"
                                        app:tl_textAllCaps="true"
                                        app:tl_textBold="BOTH"
                                        app:tl_textSelectColor="@color/text_292933"
                                        app:tl_textSelectSize="15sp"
                                        app:tl_textUnselectColor="@color/text_676773"
                                        app:tl_textsize="15sp" />

                                    <TextView
                                        android:id="@+id/tv_combo_more"
                                        android:layout_width="wrap_content"
                                        android:layout_height="wrap_content"
                                        android:drawableEnd="@drawable/icon_commodity_more"
                                        android:gravity="center"
                                        android:padding="10dp"
                                        android:text="@string/str_commodity_more"
                                        android:textColor="@color/text_9494A6"
                                        android:textSize="12sp" />
                                </LinearLayout>

                                <com.ybmmarket20.view.ViewPagerSlide
                                    android:id="@+id/vps_combo"
                                    android:layout_width="match_parent"
                                    android:layout_height="282dp"
                                    android:layout_marginStart="10dp"
                                    android:layout_marginEnd="10dp"
                                    android:layout_marginBottom="10dp" />
                            </LinearLayout>

                            <!-- 药品相关信息 -->
                            <LinearLayout
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:layout_marginTop="10dp"
                                android:background="@color/white"
                                android:gravity="center_vertical"
                                android:orientation="vertical"
                                android:paddingLeft="10dp"
                                android:paddingTop="14dp"
                                android:paddingRight="10dp"
                                android:paddingBottom="14dp">

                                <TextView
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:text="商品信息"
                                    android:textColor="@color/black"
                                    android:textSize="15sp"
                                    android:textStyle="bold" />

                                <!--保健品提示-->
                                <RelativeLayout
                                    android:id="@+id/rl_product_bjp_tip"
                                    android:layout_width="match_parent"
                                    android:layout_height="wrap_content"
                                    android:layout_marginTop="10dp"
                                    android:visibility="gone"
                                    tools:visibility="visible">

                                    <TextView
                                        style="@style/commodity_tv_style_04"
                                        android:layout_width="wrap_content"
                                        android:layout_height="match_parent"
                                        android:text="*保健食品是食品，不是药物，不能代替药物治疗疾病"
                                        android:textColor="@color/detail_tv_FF982C" />

                                </RelativeLayout>

                                <!--医保编码-->
                                <RelativeLayout
                                    android:id="@+id/rl_health_care_code"
                                    android:layout_width="match_parent"
                                    android:layout_height="wrap_content"
                                    android:layout_marginTop="10dp"
                                    android:background="@color/white"
                                    android:visibility="gone"
                                    tools:visibility="visible">

                                    <TextView
                                        android:id="@+id/tv_health_care_title"
                                        style="@style/commodity_tv_style_05"
                                        android:text="@string/detail_tv_health_care_code" />

                                    <TextView
                                        android:id="@+id/tv_health_care_code"
                                        style="@style/commodity_tv_style_04"
                                        android:layout_toRightOf="@+id/tv_health_care_title" />

                                </RelativeLayout>

                                <!--效期-->
                                <RelativeLayout
                                    android:id="@+id/rl_validity_layout"
                                    android:layout_width="match_parent"
                                    android:layout_height="wrap_content">

                                    <RelativeLayout
                                        android:id="@+id/rl_validity"
                                        android:layout_width="match_parent"
                                        android:layout_height="wrap_content"
                                        android:layout_marginTop="10dp"
                                        android:background="@color/white">

                                        <TextView
                                            android:id="@+id/tv_layout_08"
                                            style="@style/commodity_tv_style_05"
                                            android:text="有效期:" />

                                        <TextView
                                            android:id="@+id/tv_validity"
                                            style="@style/commodity_tv_style_04"
                                            android:layout_toRightOf="@+id/tv_layout_08" />

                                    </RelativeLayout>

                                </RelativeLayout>

                                <!--效期gone-->
                                <RelativeLayout
                                    android:id="@+id/rl_validity_gone"
                                    android:layout_width="match_parent"
                                    android:layout_height="wrap_content"
                                    android:layout_marginTop="10dp"
                                    android:visibility="gone">

                                    <TextView
                                        android:id="@+id/tv_layout_08_gone"
                                        style="@style/commodity_tv_style_05"
                                        android:text="@string/detail_tv_expiry_date"
                                        android:visibility="invisible" />

                                    <TextView
                                        android:id="@+id/tv_validity_gone"
                                        style="@style/commodity_tv_style_04"
                                        android:layout_alignParentTop="true"
                                        android:layout_toRightOf="@+id/tv_layout_08_gone" />

                                </RelativeLayout>


                                <!--生产日期-->
                                <RelativeLayout
                                    android:id="@+id/rl_dateOfManufacture"
                                    android:layout_width="match_parent"
                                    android:layout_height="wrap_content"
                                    android:layout_marginTop="10dp"
                                    android:background="@color/white">

                                    <TextView
                                        android:id="@+id/tv_dateOfManufacture_title"
                                        style="@style/commodity_tv_style_05"
                                        android:text="@string/detail_tv_dateOfManufacture" />

                                    <TextView
                                        android:id="@+id/tv_dateOfManufacture_content"
                                        style="@style/commodity_tv_style_04"
                                        android:layout_toRightOf="@+id/tv_dateOfManufacture_title" />


                                </RelativeLayout>

                                <LinearLayout
                                    android:id="@+id/lyCnmSkuExt"
                                    android:layout_width="match_parent"
                                    android:layout_height="wrap_content"
                                    android:orientation="vertical"
                                    android:visibility="gone" />

                                <!--中包装-->
                                <RelativeLayout
                                    android:id="@+id/rl_medium_package"
                                    android:layout_width="match_parent"
                                    android:layout_height="wrap_content"
                                    android:layout_marginTop="10dp"
                                    android:visibility="gone"
                                    tools:visibility="visible">

                                    <TextView
                                        android:id="@+id/tv_layout_02"
                                        style="@style/commodity_tv_style_05"
                                        android:text="@string/detail_tv_medium_package" />

                                    <TextView
                                        android:id="@+id/tv_medium_package"
                                        style="@style/commodity_tv_style_04"
                                        android:layout_width="wrap_content"
                                        android:layout_toRightOf="@+id/tv_layout_02" />

                                    <TextView
                                        android:id="@+id/tv_possible_to_disassemble"
                                        style="@style/commodity_tv_style"
                                        android:layout_width="wrap_content"
                                        android:layout_marginLeft="10dp"
                                        android:layout_marginTop="2dp"
                                        android:layout_marginBottom="2dp"
                                        android:layout_toRightOf="@+id/tv_medium_package"
                                        android:background="@drawable/bg_possible_to_disassemble"
                                        android:gravity="center"
                                        android:paddingLeft="5dp"
                                        android:paddingRight="5dp"
                                        android:text="不可拆零"
                                        android:textColor="@color/detail_tv_F96A25"
                                        android:textSize="9sp"
                                        android:visibility="gone" />
                                </RelativeLayout>
                                <!--件包装-->
                                <RelativeLayout
                                    android:id="@+id/rl_letter_package"
                                    android:layout_width="match_parent"
                                    android:layout_height="wrap_content"
                                    android:layout_marginTop="10dp"
                                    android:visibility="gone">

                                    <TextView
                                        android:id="@+id/tv_layout_03"
                                        style="@style/commodity_tv_style_05"
                                        android:text="@string/detail_tv_letter_package" />

                                    <TextView
                                        android:id="@+id/tv_letter_package"
                                        style="@style/commodity_tv_style_04"
                                        android:layout_toRightOf="@+id/tv_layout_03" />
                                </RelativeLayout>
                                <!--控销价格-->
                                <RelativeLayout
                                    android:id="@+id/rl_control"
                                    android:layout_width="match_parent"
                                    android:layout_height="wrap_content"
                                    android:layout_marginTop="10dp"
                                    android:visibility="gone">

                                    <TextView
                                        android:id="@+id/tv_control_layout_03"
                                        style="@style/commodity_tv_style_05"
                                        android:text="@string/detail_tv_control_price" />

                                    <TextView
                                        android:id="@+id/tv_control_price"
                                        style="@style/commodity_tv_style_04"
                                        android:layout_toRightOf="@+id/tv_control_layout_03" />
                                </RelativeLayout>
                                <!--建议零售价-->
                                <RelativeLayout
                                    android:id="@+id/rl_suggested_retail_price"
                                    android:layout_width="match_parent"
                                    android:layout_height="wrap_content"
                                    android:layout_marginTop="10dp"
                                    android:visibility="gone">

                                    <TextView
                                        android:id="@+id/tv_layout_04"
                                        style="@style/commodity_tv_style_05"
                                        android:text="@string/detail_tv_suggested_retail_price" />

                                    <TextView
                                        android:id="@+id/tv_suggested_retail_price"
                                        style="@style/commodity_tv_style_04"
                                        android:layout_toRightOf="@+id/tv_layout_04" />
                                </RelativeLayout>
                                <!--生产厂家-->
                                <RelativeLayout
                                    android:id="@+id/rl_manufacturer"
                                    android:layout_width="match_parent"
                                    android:layout_height="wrap_content"
                                    android:layout_marginTop="10dp"
                                    android:visibility="gone">

                                    <TextView
                                        android:id="@+id/tv_layout_05"
                                        style="@style/commodity_tv_style_05"
                                        android:text="@string/detail_tv_manufacturer" />

                                    <TextView
                                        android:id="@+id/tv_manufacturer"
                                        style="@style/commodity_tv_style_04"
                                        android:layout_toRightOf="@+id/tv_layout_05"
                                        android:singleLine="false" />

                                </RelativeLayout>
                                <!--医保代码-->
                                <RelativeLayout
                                    android:id="@+id/rl_medical_insurance_code"
                                    android:layout_width="match_parent"
                                    android:layout_height="wrap_content"
                                    android:layout_marginTop="10dp"
                                    android:visibility="gone">

                                    <TextView
                                        android:id="@+id/tv_medical_insurance_code"
                                        style="@style/commodity_tv_style_05"
                                        android:text="@string/detail_tv_medical_insurance_code" />

                                    <TextView
                                        android:id="@+id/medical_insurance_code_content"
                                        style="@style/commodity_tv_style_04"
                                        android:layout_toRightOf="@+id/tv_medical_insurance_code"
                                        android:layout_toLeftOf="@id/tv_medical_insurance_copy"
                                        android:singleLine="true"
                                        />

                                    <TextView
                                        android:id="@+id/tv_medical_insurance_copy"
                                        android:layout_width="wrap_content"
                                        android:layout_height="wrap_content"
                                        android:layout_alignParentRight="true"
                                        android:layout_centerVertical="true"
                                        android:layout_gravity="center_vertical"
                                        android:layout_marginRight="10dp"
                                        android:background="@drawable/order_gray_border"
                                        android:gravity="center_vertical"
                                        android:paddingLeft="13dp"
                                        android:paddingTop="3dp"
                                        android:paddingRight="13dp"
                                        android:paddingBottom="3dp"
                                        android:text="复制"
                                        android:textColor="@color/text_292933"
                                        android:textSize="14sp" />

                                </RelativeLayout>
                                <!--产地-->
                                <RelativeLayout
                                    android:id="@+id/rl_producer"
                                    android:layout_width="match_parent"
                                    android:layout_height="wrap_content"
                                    android:layout_marginTop="10dp"
                                    android:visibility="visible">

                                    <TextView
                                        android:id="@+id/tv_producer"
                                        style="@style/commodity_tv_style_05"
                                        android:text="@string/detail_tv_producer" />

                                    <TextView
                                        android:id="@+id/tv_producer_content"
                                        style="@style/commodity_tv_style_04"
                                        android:layout_centerVertical="false"
                                        android:layout_toRightOf="@+id/tv_producer" />

                                </RelativeLayout>
                                <!--批准文号-->
                                <RelativeLayout
                                    android:id="@+id/rl_approval_number"
                                    android:layout_width="match_parent"
                                    android:layout_height="wrap_content"
                                    android:layout_marginTop="10dp"
                                    android:visibility="gone">

                                    <TextView
                                        android:id="@+id/tv_layout_06"
                                        style="@style/commodity_tv_style_05"
                                        android:text="@string/detail_tv_approval_number" />

                                    <TextView
                                        android:id="@+id/tv_approval_number"
                                        style="@style/commodity_tv_style_04"
                                        android:layout_toRightOf="@+id/tv_layout_06" />

                                    <TextView
                                        android:id="@+id/tv_approval_number2"
                                        style="@style/commodity_tv_style_04"
                                        android:layout_toRightOf="@+id/tv_layout_06"
                                        android:singleLine="false"
                                        android:visibility="gone" />

                                </RelativeLayout>
                                <!--毛利率-->
                                <RelativeLayout
                                    android:id="@+id/rl_grossMargin"
                                    android:layout_width="match_parent"
                                    android:layout_height="wrap_content"
                                    android:layout_marginTop="10dp"
                                    android:visibility="gone">

                                    <TextView
                                        android:id="@+id/tv_layout_09"
                                        style="@style/commodity_tv_style_05"
                                        android:text="@string/detail_tv_gross_margin" />

                                    <TextView
                                        android:id="@+id/tv_grossMargin"
                                        style="@style/commodity_tv_style_04"
                                        android:layout_toRightOf="@+id/tv_layout_09" />

                                </RelativeLayout>
                                <!--运费-->
                                <RelativeLayout
                                    android:id="@+id/rl_freight_tips"
                                    android:layout_width="match_parent"
                                    android:layout_height="wrap_content"
                                    android:layout_marginTop="10dp"
                                    android:visibility="gone">

                                    <TextView
                                        android:id="@+id/tv_layout_freight_tips"
                                        style="@style/commodity_tv_style_05"
                                        android:text="@string/detail_tv_freight_tips" />

                                    <TextView
                                        android:id="@+id/tv_freight_tips"
                                        style="@style/commodity_tv_style_04"
                                        android:layout_toRightOf="@+id/tv_layout_freight_tips"
                                        android:singleLine="false" />
                                </RelativeLayout>
                                <!--追溯码-->
                                <RelativeLayout
                                    android:id="@+id/rl_traceable_tips"
                                    android:layout_width="match_parent"
                                    android:layout_height="wrap_content"
                                    android:layout_marginTop="10dp"
                                    android:visibility="gone">

                                    <TextView
                                        android:id="@+id/tv_layout_traceable_tips"
                                        style="@style/commodity_tv_style_05"
                                        android:text="@string/detail_tv_traceable_tips" />

                                    <TextView
                                        android:id="@+id/tv_traceable_tips"
                                        style="@style/commodity_tv_style_04"
                                        android:layout_toRightOf="@+id/tv_layout_traceable_tips"
                                        android:singleLine="false"
                                        android:text="有" />
                                </RelativeLayout>

                                <TextView
                                    android:layout_width="match_parent"
                                    android:layout_height="wrap_content"
                                    android:text="@string/str_commodity_yb_tips"
                                    android:textColor="@color/text_color_999999"
                                    android:textSize="13sp" />
                            </LinearLayout>

                            <!-- 药品配送信息 -->
                            <LinearLayout
                                android:id="@+id/ll_delivery_instructions"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:layout_marginTop="10dp"
                                android:background="@color/white"
                                android:gravity="center_vertical"
                                android:orientation="vertical"
                                android:paddingLeft="10dp"
                                android:paddingTop="14dp"
                                android:paddingRight="10dp"
                                android:paddingBottom="14dp">

                                <TextView
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:text="配送说明"
                                    android:textColor="@color/black"
                                    android:textSize="15sp"
                                    android:textStyle="bold" />

                                <!--配送方式-->
                                <RelativeLayout
                                    android:id="@+id/rl_delivery_method_layout"
                                    android:layout_width="match_parent"
                                    android:layout_height="wrap_content"
                                    android:layout_marginTop="10dp"
                                    android:background="@color/white">

                                    <TextView
                                        android:id="@+id/tv_delivery_method_title"
                                        style="@style/commodity_tv_style_05"
                                        android:text="@string/detail_tv_delivery_method" />

                                    <TextView
                                        android:id="@+id/tv_delivery_method"
                                        style="@style/commodity_tv_style_04"
                                        android:layout_toRightOf="@+id/tv_delivery_method_title" />

                                </RelativeLayout>

                                <!--起配包邮-->
                                <RelativeLayout
                                    android:id="@+id/rl_parcel_layout"
                                    android:layout_width="match_parent"
                                    android:layout_height="wrap_content"
                                    android:layout_marginTop="10dp">

                                    <TextView
                                        android:id="@+id/tv_parcel_title"
                                        style="@style/commodity_tv_style_05"
                                        android:text="@string/detail_tv_parcel_title" />

                                    <TextView
                                        android:id="@+id/tv_parcel"
                                        style="@style/commodity_tv_style_04"
                                        android:layout_toRightOf="@+id/tv_parcel_title" />

                                </RelativeLayout>

                                <!--发货省市-->
                                <RelativeLayout
                                    android:id="@+id/rl_shipping_province_city_layout"
                                    android:layout_width="match_parent"
                                    android:layout_height="wrap_content"
                                    android:layout_marginTop="10dp">

                                    <TextView
                                        android:id="@+id/tv_shipping_province_city_title"
                                        style="@style/commodity_tv_style_05"
                                        android:text="@string/detail_tv_shipping_province_city" />

                                    <TextView
                                        android:id="@+id/tv_shipping_province_city"
                                        style="@style/commodity_tv_style_04"
                                        android:layout_alignParentTop="true"
                                        android:layout_toRightOf="@+id/tv_shipping_province_city_title" />

                                </RelativeLayout>

                                <!--发货时间-->
                                <RelativeLayout
                                    android:id="@+id/rl_delivery_time_layout"
                                    android:layout_width="match_parent"
                                    android:layout_height="wrap_content"
                                    android:layout_marginTop="10dp"
                                    android:background="@color/white">

                                    <TextView
                                        android:id="@+id/tv_delivery_time_title"
                                        style="@style/commodity_tv_style_05"
                                        android:text="@string/detail_tv_delivery_time" />

                                    <TextView
                                        android:id="@+id/tv_delivery_time"
                                        style="@style/commodity_tv_style_04"
                                        android:layout_toRightOf="@+id/tv_delivery_time_title"
                                        android:singleLine="false" />

                                </RelativeLayout>

                            </LinearLayout>

                            <LinearLayout
                                android:id="@+id/ll_service_tag"
                                android:layout_width="match_parent"
                                android:layout_height="40dp"
                                android:layout_gravity="center_vertical"
                                android:background="@color/white"
                                android:gravity="center_vertical"
                                android:orientation="horizontal"
                                android:paddingLeft="10dp"
                                android:paddingRight="5dp"
                                android:paddingBottom="14dp">

                                <TextView
                                    style="@style/commodity_tv_style_05"
                                    android:text="服务:" />

                                <com.ybmmarket20.view.MyGridView
                                    android:id="@+id/pl_service"
                                    android:layout_width="0dp"
                                    android:layout_height="match_parent"
                                    android:layout_weight="1"
                                    android:numColumns="3"
                                    android:visibility="gone" />

                                <ImageView
                                    android:id="@+id/iv_service"
                                    style="@style/detail_iv_base"
                                    android:src="@drawable/icon_detail_right_c" />

                            </LinearLayout>

                        </LinearLayout>

                        <!--第三方店铺-->
                        <LinearLayout
                            android:id="@+id/ll_company_name"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:orientation="vertical"
                            android:visibility="gone"
                            tools:visibility="visible">

                            <include layout="@layout/layout_commodity_pop_shop" />
                        </LinearLayout>
                        <!--店铺项目自营-->
                        <LinearLayout
                            android:id="@+id/rl_self_shop"
                            android:layout_width="match_parent"
                            android:layout_height="@dimen/dimen_dp_89"
                            android:layout_marginTop="10dp"
                            android:visibility="gone"
                            tools:visibility="visible">

                            <include layout="@layout/layout_commodity_self_shop" />
                        </LinearLayout>

                        <LinearLayout
                            android:id="@+id/rl_recommend"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:minHeight="465dp"
                            android:orientation="vertical">

                            <TextView
                                android:id="@+id/tv_recommend"
                                style="@style/commodity_tv_style"
                                android:layout_height="45dp"
                                android:layout_marginTop="10dp"
                                android:background="@color/white"
                                android:gravity="center_vertical"
                                android:paddingLeft="10dp"
                                android:paddingTop="10dp"
                                android:text="热销精选"
                                android:textColor="@color/color_292933"
                                android:textSize="14sp"
                                android:textStyle="bold" />

                            <com.ybmmarket20.view.CommodityRecommendLayout
                                android:id="@+id/recommend_layout"
                                android:layout_width="match_parent"
                                android:layout_height="430dp"
                                android:background="@color/white" />

                        </LinearLayout>

                    </LinearLayout>


                </androidx.constraintlayout.widget.ConstraintLayout>

            </LinearLayout>

            <View
                android:layout_width="match_parent"
                android:layout_height="@dimen/dimen_dp_10"
                android:background="@color/color_F7F7F8" />

            <LinearLayout
                android:id="@+id/ll_instructions"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:background="@color/white"
                android:orientation="vertical">

                <!--文字说明书-->
                <LinearLayout
                    android:id="@+id/two"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:background="@color/white"
                    android:orientation="vertical">

                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="45dp"
                        android:gravity="center_vertical"
                        android:paddingLeft="10dp"
                        android:paddingRight="10dp"
                        android:text="说明书"
                        android:textColor="@color/color_292933"
                        android:textSize="14sp"
                        android:textStyle="bold" />

                    <RelativeLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:descendantFocusability="blocksDescendants">

                        <com.ybmmarket20.view.CommodityRecyclerLayout
                            android:id="@+id/specification_list"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:background="@color/white" />

                    </RelativeLayout>

                </LinearLayout>

                <!--图文描述-->
                <LinearLayout
                    android:id="@+id/three"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical">

                    <!--产品说明书-->
                    <LinearLayout
                        android:id="@+id/ll_specification"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="10dp"
                        android:background="@color/white"
                        android:orientation="vertical">

                        <View
                            android:layout_width="match_parent"
                            android:layout_height="@dimen/dimen_dp_10"
                            android:background="@color/color_F7F7F8" />

                        <TextView
                            android:layout_width="match_parent"
                            android:layout_height="35dp"
                            android:background="@color/white"
                            android:gravity="center_vertical"
                            android:paddingLeft="10dp"
                            android:text="图文描述"
                            android:textColor="@color/color_292933"
                            android:textSize="14sp"
                            android:textStyle="bold" />

                        <TextView
                            android:id="@+id/ll_specification_tv"
                            android:layout_width="match_parent"
                            android:layout_height="45dp"
                            android:background="@drawable/product_text_bg_2"
                            android:gravity="center"
                            android:text="点击图片可查看大图"
                            android:textColor="@color/color_292933"
                            android:textSize="14sp" />

                    </LinearLayout>

                    <com.ybmmarket20.view.ImageLayout
                        android:id="@+id/il_specification"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:background="@color/white" />

                    <View
                        android:layout_width="match_parent"
                        android:layout_height="@dimen/dimen_dp_10"
                        android:background="@color/color_F7F7F8" />
                    <!--服务说明-->
                    <include layout="@layout/layout_service_info" />

                    <View
                        android:id="@+id/v_about_divider"
                        android:layout_width="match_parent"
                        android:layout_height="@dimen/dimen_dp_10"
                        android:background="@color/color_F7F7F8" />
                    <!--关于药帮忙-->
                    <LinearLayout
                        android:id="@+id/ll_about"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="10dp"
                        android:layout_marginBottom="20dp"
                        android:background="@color/white"
                        android:orientation="vertical">

                        <ImageView
                            android:id="@+id/il_about"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:scaleType="fitXY"
                            android:src="@drawable/icon_announcement" />

                    </LinearLayout>

                </LinearLayout>
            </LinearLayout>

            <View
                android:layout_width="match_parent"
                android:layout_height="@dimen/dimen_dp_10"
                android:background="@color/color_F7F7F8" />

            <LinearLayout
                android:id="@+id/ll_module_recommend"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:background="@color/white"
                android:orientation="vertical">

                <TextView
                    android:id="@+id/tv_recommend_tips"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/dimen_dp_10"
                    android:layout_marginTop="@dimen/dimen_dp_13"
                    android:text="@string/maybe_like"
                    android:textColor="@color/color_292933"
                    android:textSize="@dimen/dimen_dp_15"
                    android:textStyle="bold" />

            </LinearLayout>

            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/crv_recommend"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_marginBottom="0dp"
                android:paddingStart="@dimen/dimen_dp_13"
                android:paddingEnd="@dimen/dimen_dp_13" />
        </LinearLayout>
    </androidx.core.widget.NestedScrollView>
    <!--近效期、临期商品不退换提示语位置优化-->
    <TextView
        android:id="@+id/tv_tip_optimize"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_gravity="bottom"
        android:background="@color/colors_ffe8e8"
        android:paddingStart="@dimen/dimen_dp_10"
        android:paddingTop="@dimen/dimen_dp_7"
        android:paddingEnd="@dimen/dimen_dp_10"
        android:paddingBottom="@dimen/dimen_dp_7"
        android:text="@string/tips_optimize_product_detail"
        android:textColor="@color/color_ff2121"
        android:textSize="@dimen/dimen_dp_12"
        android:visibility="gone" />
    <!--操作栏-->
    <include
        layout="@layout/detail_operation_tool_commodity"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent" />

</LinearLayout>

